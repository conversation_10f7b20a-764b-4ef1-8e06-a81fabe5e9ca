<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MetaMask Configuration for Verifai</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step h3 {
            color: #2d3748;
            margin-top: 0;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #c6f6d5; color: #22543d; }
        .error { background: #fed7d7; color: #742a2a; }
        .warning { background: #fefcbf; color: #744210; }
        .info { background: #bee3f8; color: #2a4365; }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦊 MetaMask Configuration for Verifai</h1>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="status"></div>
        
        <div class="step">
            <h3>Step 1: Check MetaMask Installation</h3>
            <p>First, let's verify that MetaMask is installed and accessible.</p>
            <button onclick="checkMetaMask()">Check MetaMask</button>
        </div>
        
        <div class="step">
            <h3>Step 2: Add Hardhat Network</h3>
            <p>Add the local Hardhat network to MetaMask with the correct configuration.</p>
            <button onclick="addHardhatNetwork()" id="addNetworkBtn" disabled>Add Hardhat Network</button>
        </div>
        
        <div class="step">
            <h3>Step 3: Switch to Hardhat Network</h3>
            <p>Switch MetaMask to use the Hardhat local network.</p>
            <button onclick="switchToHardhat()" id="switchNetworkBtn" disabled>Switch to Hardhat</button>
        </div>
        
        <div class="step">
            <h3>Step 4: Import Test Account</h3>
            <p>Import the Hardhat test account with 10,000 ETH for development.</p>
            <div class="code">
                Private Key: 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
            </div>
            <p><strong>Note:</strong> Copy the private key above and manually import it in MetaMask: Account Menu → Import Account → Private Key</p>
        </div>
        
        <div class="step">
            <h3>Step 5: Verify Configuration</h3>
            <p>Test the complete configuration to ensure everything is working.</p>
            <button onclick="verifyConfiguration()" id="verifyBtn" disabled>Verify Configuration</button>
        </div>
        
        <div class="step">
            <h3>Emergency Reset</h3>
            <p>If you encounter issues, reset your MetaMask account.</p>
            <button onclick="showResetInstructions()" style="background: #e53e3e;">Reset Instructions</button>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 5;
        
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }
        
        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }
        
        async function checkMetaMask() {
            try {
                log("🔍 Checking MetaMask installation...");
                
                if (typeof window.ethereum === 'undefined') {
                    log("❌ MetaMask not detected. Please install MetaMask extension.", 'error');
                    return;
                }
                
                if (!window.ethereum.isMetaMask) {
                    log("⚠️ MetaMask not detected as primary provider.", 'warning');
                }
                
                log("✅ MetaMask detected successfully!", 'success');
                
                // Request account access
                const accounts = await window.ethereum.request({ 
                    method: 'eth_requestAccounts' 
                });
                
                if (accounts.length > 0) {
                    log(`✅ Connected to account: ${accounts[0]}`, 'success');
                    currentStep = 1;
                    updateProgress();
                    document.getElementById('addNetworkBtn').disabled = false;
                } else {
                    log("❌ No accounts found. Please unlock MetaMask.", 'error');
                }
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }
        
        async function addHardhatNetwork() {
            try {
                log("🔧 Adding Hardhat network to MetaMask...");
                
                await window.ethereum.request({
                    method: 'wallet_addEthereumChain',
                    params: [{
                        chainId: '0x539', // 1337 in hex
                        chainName: 'Hardhat Local',
                        rpcUrls: ['http://127.0.0.1:8545'],
                        nativeCurrency: {
                            name: 'Ethereum',
                            symbol: 'ETH',
                            decimals: 18
                        }
                    }]
                });
                
                log("✅ Hardhat network added successfully!", 'success');
                currentStep = 2;
                updateProgress();
                document.getElementById('switchNetworkBtn').disabled = false;
                
            } catch (error) {
                if (error.code === 4902) {
                    log("ℹ️ Network already exists in MetaMask.", 'info');
                    currentStep = 2;
                    updateProgress();
                    document.getElementById('switchNetworkBtn').disabled = false;
                } else {
                    log(`❌ Error adding network: ${error.message}`, 'error');
                }
            }
        }
        
        async function switchToHardhat() {
            try {
                log("🔄 Switching to Hardhat network...");
                
                await window.ethereum.request({
                    method: 'wallet_switchEthereumChain',
                    params: [{ chainId: '0x539' }]
                });
                
                log("✅ Switched to Hardhat network successfully!", 'success');
                currentStep = 3;
                updateProgress();
                document.getElementById('verifyBtn').disabled = false;
                
                // Check balance
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                if (accounts.length > 0) {
                    const balance = await window.ethereum.request({
                        method: 'eth_getBalance',
                        params: [accounts[0], 'latest']
                    });
                    const ethBalance = parseInt(balance, 16) / Math.pow(10, 18);
                    log(`💰 Account balance: ${ethBalance.toFixed(2)} ETH`, 'info');
                }
                
            } catch (error) {
                log(`❌ Error switching network: ${error.message}`, 'error');
            }
        }
        
        async function verifyConfiguration() {
            try {
                log("🔍 Verifying complete configuration...");
                
                // Check network
                const chainId = await window.ethereum.request({ method: 'eth_chainId' });
                if (chainId !== '0x539') {
                    log(`❌ Wrong network. Expected 0x539, got ${chainId}`, 'error');
                    return;
                }
                log("✅ Correct network (Hardhat Local)", 'success');
                
                // Check account
                const accounts = await window.ethereum.request({ method: 'eth_accounts' });
                if (accounts.length === 0) {
                    log("❌ No accounts connected", 'error');
                    return;
                }
                log(`✅ Account connected: ${accounts[0]}`, 'success');
                
                // Check contract
                const CONTRACT_ADDRESS = "******************************************";
                const code = await window.ethereum.request({
                    method: 'eth_getCode',
                    params: [contractAddress, 'latest']
                });
                
                if (code === '0x') {
                    log(`❌ Contract not found at ${contractAddress}. Please deploy the contract.`, 'error');
                    log("Run: npx hardhat run scripts/deploy.js --network localhost", 'warning');
                } else {
                    log(`✅ Contract found at ${contractAddress}`, 'success');
                }
                
                // Check balance
                const balance = await window.ethereum.request({
                    method: 'eth_getBalance',
                    params: [accounts[0], 'latest']
                });
                const ethBalance = parseInt(balance, 16) / Math.pow(10, 18);
                
                if (ethBalance < 1) {
                    log(`⚠️ Low balance: ${ethBalance.toFixed(4)} ETH. Import test account for more ETH.`, 'warning');
                } else {
                    log(`✅ Sufficient balance: ${ethBalance.toFixed(2)} ETH`, 'success');
                }
                
                currentStep = 5;
                updateProgress();
                
                log("🎉 Configuration verification complete! You can now use Verifai.", 'success');
                
            } catch (error) {
                log(`❌ Verification error: ${error.message}`, 'error');
            }
        }
        
        function showResetInstructions() {
            log("🚨 MetaMask Reset Instructions:", 'warning');
            log("1. Open MetaMask → Settings → Advanced → Reset Account", 'info');
            log("2. Clear browser cache (F12 → Application → Storage → Clear)", 'info');
            log("3. Refresh this page and start over", 'info');
            log("4. If issues persist, restart Hardhat node and redeploy contract", 'info');
        }
        
        // Initialize
        updateProgress();
        log("Welcome to Verifai MetaMask Configuration! Click 'Check MetaMask' to begin.", 'info');
    </script>
</body>
</html>
