{"name": "fyp", "version": "1.0.0", "description": "This project demonstrates a basic Hardhat use case. It comes with a sample contract, a test for that contract, and a Hardhat Ignition module that deploys that contract.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "ganache": "node start-ganache.js", "deploy:ganache": "npx hardhat run scripts/deploy-ganache.js --network ganache", "deploy:localhost": "npx hardhat run scripts/deploy.js --network localhost", "start:ganache": "npm run ganache", "setup:ganache": "npm run ganache & sleep 5 && npm run deploy:ganache", "deploy-and-setup": "node scripts/auto-restart-setup.js", "restart-fix": "node scripts/auto-restart-setup.js", "quick-deploy": "node scripts/auto-restart-setup.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@ethersproject/providers": "^5.8.0", "@nomicfoundation/hardhat-chai-matchers": "^1.0.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-toolbox": "^2.0.2", "@nomiclabs/hardhat-ethers": "^2.0.0", "@nomiclabs/hardhat-etherscan": "^3.0.0", "@typechain/ethers-v5": "^10.1.0", "@typechain/hardhat": "^6.1.2", "@types/chai": "^4.2.0", "@types/mocha": "^9.1.0", "chai": "^4.2.0", "dotenv": "^16.5.0", "ethers": "^5.4.7", "hardhat": "^2.24.0", "hardhat-gas-reporter": "^1.0.8", "solidity-coverage": "^0.8.1", "ts-node": "^10.9.1", "typechain": "^8.1.0", "typescript": "^4.9.5"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0"}, "directories": {"test": "test"}, "type": "commonjs"}