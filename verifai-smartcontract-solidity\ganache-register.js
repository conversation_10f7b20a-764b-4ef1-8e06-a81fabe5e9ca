const hre = require("hardhat");

async function main() {
    console.log("🚀 Registering test product with Ganache...");
    
    try {
        // Contract address
        const contractAddress = "******************************************";
        
        // Get contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("📋 Contract Address:", contractAddress);
        console.log("✅ Connected to contract successfully");
        
        // Product details
        const productData = {
            name: "Demo Product",
            brand: "Demo Brand", 
            serialNumber: "DEMO123",
            description: "Test product for scanning demo",
            image: "demo.jpg",
            actor: "Demo Manufacturer",
            location: "Demo Location",
            timestamp: Math.floor(Date.now() / 1000).toString()
        };
        
        console.log("📦 Registering product:", productData.serialNumber);
        
        // Register the product
        const registerTx = await contract.registerProduct(
            productData.name,
            productData.brand,
            productData.serialNumber,
            productData.description,
            productData.image,
            productData.actor,
            productData.location,
            productData.timestamp
        );
        
        console.log("⏳ Transaction hash:", registerTx.hash);
        await registerTx.wait();
        console.log("✅ Product registered successfully!");
        
        // Verify the product
        const product = await contract.getProduct(productData.serialNumber);
        console.log("✅ Product verified!");
        console.log("📦 Product details:");
        console.log("   Serial Number:", product[0]);
        console.log("   Name:", product[1]);
        console.log("   Brand:", product[2]);
        console.log("   Description:", product[3]);
        console.log("   Image:", product[4]);
        
        console.log("\n🎯 QR Code for testing:");
        console.log(`${contractAddress},${productData.serialNumber}`);
        
        console.log("\n✅ SUCCESS! Your demo product is ready!");
        console.log("🔗 You can now test this URL:");
        console.log(`http://localhost:5173/product?data=${contractAddress},${productData.serialNumber}`);
        
    } catch (error) {
        console.error("❌ Error:", error.message);
        
        if (error.message.includes("insufficient funds")) {
            console.log("\n💡 Solution:");
            console.log("1. Make sure Ganache is running");
            console.log("2. Import a Ganache account to MetaMask");
            console.log("3. Make sure the account has ETH");
        } else if (error.message.includes("ECONNREFUSED")) {
            console.log("\n💡 Make sure Ganache is running on http://127.0.0.1:7545");
        }
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Script failed:", error);
        process.exit(1);
    });
