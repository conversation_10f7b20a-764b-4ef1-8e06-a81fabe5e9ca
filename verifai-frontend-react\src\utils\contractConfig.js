// Auto-generated contract configuration - DO NOT EDIT MANUALLY
// This file is automatically updated by update-contract-address.js
export const CONTRACT_ADDRESS = "******************************************";
export const NETWORK_CONFIG = {
    chainId: 1337,
    chainName: "Ganache Local",
    rpcUrl: "http://127.0.0.1:7545",
    nativeCurrency: {
        name: "Ethereum",
        symbol: "ETH",
        decimals: 18
    }
};

export const GANACHE_ACCOUNTS = [
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************"
];

export default {
    CONTRACT_ADDRESS,
    NETWORK_CONFIG,
    GANACHE_ACCOUNTS
};
