import {
  <PERSON>,
  Typo<PERSON>,
  Alert,
  Container,
  Stack,
  alpha,
  useTheme
} from '@mui/material';
import { useState } from 'react';
import useAuth from '../../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import {
  QrCodeScanner,
  ArrowBack,
  Refresh,
  Security,
  Fingerprint
} from '@mui/icons-material';
import QrScanner from '../QrScanner';
import AlternativeQrScanner from '../AlternativeQrScanner';
import { styled, keyframes } from '@mui/material/styles';
import ElegantBackground from '../common/ElegantBackground';

// Professional animations for scanner
const smoothSlideIn = keyframes`
  0% {
    transform: translateY(40px);
    opacity: 0;
    filter: blur(10px);
  }
  100% {
    transform: translateY(0);
    opacity: 1;
    filter: blur(0);
  }
`;

const gentleFloat = keyframes`
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-4px) rotate(1deg);
  }
  50% {
    transform: translateY(-8px) rotate(0deg);
  }
  75% {
    transform: translateY(-4px) rotate(-1deg);
  }
`;

const dataFlow = keyframes`
  0% {
    transform: translateX(-100%) scaleX(0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) scaleX(1);
    opacity: 0;
  }
`;





// Professional Clean Scan Area
const ScanArea = styled(Box)(({ theme }) => ({
  border: `2px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '20px',
  padding: theme.spacing(6),
  minHeight: '480px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  background: theme.palette.mode === 'dark' ? '#1a1a1a' : '#ffffff',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  boxShadow: `
    0 8px 32px ${alpha(theme.palette.common.black, 0.08)},
    0 0 0 1px ${alpha(theme.palette.divider, 0.1)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.1)}
  `,
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `
      0 12px 40px ${alpha(theme.palette.common.black, 0.12)},
      0 0 0 1px ${alpha(theme.palette.primary.main, 0.2)},
      inset 0 1px 0 ${alpha(theme.palette.common.white, 0.15)}
    `,
    borderColor: alpha(theme.palette.primary.main, 0.3),
  },
}));

// Professional Status Card
const StatusCard = styled(Box)(({ theme }) => ({
  background: `
    linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.9)} 0%,
      ${alpha(theme.palette.background.paper, 0.7)} 100%
    )
  `,
  backdropFilter: 'blur(20px) saturate(150%)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: '16px',
  padding: '16px 24px',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  boxShadow: `
    0 8px 32px ${alpha(theme.palette.common.black, 0.08)},
    inset 0 1px 0 ${alpha(theme.palette.common.white, 0.1)}
  `,
  '&:hover': {
    transform: 'translateY(-2px) scale(1.02)',
    boxShadow: `
      0 12px 40px ${alpha(theme.palette.common.black, 0.12)},
      0 0 20px ${alpha(theme.palette.primary.main, 0.1)},
      inset 0 1px 0 ${alpha(theme.palette.common.white, 0.15)}
    `,
    border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

const ScannerPage = () => {
  const CONTRACT_ADDRESS = process.env.REACT_APP_CONTRACT_ADDRESS || "******************************************";
  const [qrData, setQrData] = useState('');
  const [scanning, setScanning] = useState(true);
  const [error, setError] = useState(null);
  const [useAlternativeScanner, setUseAlternativeScanner] = useState(false);
  const { auth, user } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();

  const passData = (data) => {
    setQrData(data);
    setScanning(false);
    console.log("QR data received:", data);

    // Immediate routing without delay
    handleQRDataRouting(data);
  };

  const handleQRDataRouting = (data) => {
    const arr = data.split(',');
    const contractAddress = arr[0];

    console.log("Scanned contract address:", contractAddress);
    console.log("Expected contract address:", CONTRACT_ADDRESS);
    console.log("Addresses match:", contractAddress === CONTRACT_ADDRESS);

    if (contractAddress) {
      if (contractAddress === CONTRACT_ADDRESS) {
        // Get user role from correct location
        const userRole = auth?.user?.role || user?.role;
        console.log("User role for routing:", userRole);

        // Use setTimeout with minimal delay to ensure state updates complete
        setTimeout(() => {
          if (userRole === 'supplier' || userRole === 'retailer') {
            console.log("Routing to update-product for role:", userRole);
            navigate('/update-product', { state: { qrData: data } });
          } else {
            console.log("Routing to authentic-product for role:", userRole);
            navigate('/authentic-product', { state: { qrData: data } });
          }
        }, 100); // Minimal delay for smooth transition
      } else {
        console.log("Invalid contract address, routing to fake-product");
        console.log(`Expected: ${CONTRACT_ADDRESS}, Got: ${contractAddress}`);
        setTimeout(() => {
          navigate('/fake-product');
        }, 100);
      }
    }
  };

  const handleBack = () => navigate(-1);
  const handleRescan = () => {
    setQrData('');
    setScanning(true);
    setError(null);
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      background: `
        linear-gradient(135deg,
          #ffffff 0%,
          #fafafa 25%,
          #f5f5f5 50%,
          #fafafa 75%,
          #ffffff 100%
        ),
        radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.02)} 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, ${alpha(theme.palette.secondary.main, 0.02)} 0%, transparent 50%)
      `,
      position: 'relative',
      overflow: 'hidden',
    }}>
      {/* Elegant Scanner Background Animation */}
      <ElegantBackground variant="scanner" intensity="medium" />
      <Container maxWidth="lg" sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        py: 6,
        position: 'relative',
        zIndex: 10
      }}>
        <Box sx={{
          maxWidth: 800,
          width: '100%',
          background: theme.palette.mode === 'dark' ? '#1a1a1a' : '#ffffff',
          backdropFilter: 'blur(30px) saturate(180%)',
          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
          borderRadius: '32px',
          p: 8,
          animation: `${smoothSlideIn} 1s cubic-bezier(0.4, 0, 0.2, 1)`,
          boxShadow: `
            0 32px 80px ${alpha(theme.palette.common.black, 0.12)},
            0 0 0 1px ${alpha(theme.palette.primary.main, 0.1)},
            inset 0 1px 0 ${alpha(theme.palette.common.white, 0.2)}
          `,
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: `linear-gradient(90deg,
              ${theme.palette.primary.main} 0%,
              ${theme.palette.info.main} 50%,
              ${theme.palette.primary.dark} 100%
            )`,
            borderRadius: '32px 32px 0 0',
          },
        }}>
          {/* Enhanced Header */}
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Box
              sx={{
                width: 120,
                height: 120,
                mx: 'auto',
                mb: 4,
                borderRadius: '50%',
                background: `
                  linear-gradient(135deg,
                    ${theme.palette.primary.main} 0%,
                    ${theme.palette.primary.light} 25%,
                    ${theme.palette.info.main} 50%,
                    ${theme.palette.primary.dark} 75%,
                    ${theme.palette.primary.main} 100%
                  )
                `,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                animation: `${gentleFloat} 6s ease-in-out infinite`,
                boxShadow: `
                  0 20px 60px ${alpha(theme.palette.primary.main, 0.3)},
                  0 0 0 4px ${alpha(theme.palette.primary.main, 0.1)},
                  inset 0 2px 0 ${alpha(theme.palette.common.white, 0.3)}
                `,
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'scale(1.05)',
                  boxShadow: `
                    0 25px 80px ${alpha(theme.palette.primary.main, 0.4)},
                    0 0 0 6px ${alpha(theme.palette.primary.main, 0.15)},
                    inset 0 2px 0 ${alpha(theme.palette.common.white, 0.4)}
                  `,
                },
              }}
            >
              <QrCodeScanner sx={{ fontSize: 48, color: 'white' }} />
            </Box>

            <Typography
              variant="h2"
              component="h1"
              sx={{
                mb: 3,
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                fontWeight: 800,
                background: `linear-gradient(135deg,
                  ${theme.palette.primary.main} 0%,
                  ${theme.palette.info.main} 50%,
                  ${theme.palette.primary.dark} 100%
                )`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                letterSpacing: '-0.02em',
                fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
              }}
            >
              Product Authentication
            </Typography>

            <Typography
              variant="h6"
              sx={{
                mb: 2,
                color: theme.palette.primary.main,
                fontWeight: 700,
                fontSize: '1.2rem',
                letterSpacing: '0.1em',
                textTransform: 'uppercase',
                fontFamily: '"JetBrains Mono", "Fira Code", monospace',
                position: 'relative',
                '&::before': {
                  content: '">"',
                  color: theme.palette.primary.main,
                  marginRight: '8px',
                  fontWeight: 900,
                },
              }}
            >
              Blockchain Verification Protocol Active
            </Typography>

            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                mb: 5,
                maxWidth: 600,
                mx: 'auto',
                fontWeight: 500,
                fontSize: '1.1rem',
                lineHeight: 1.6,
                fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
              }}
            >
              Position the QR code within the scanning area to initiate secure blockchain verification and product authentication
            </Typography>

            {/* Enhanced User Role Information */}
            {(auth?.user?.role || user?.role) && (
              <Box sx={{ mb: 5 }}>
                <StatusCard sx={{
                  maxWidth: 500,
                  mx: 'auto',
                  background: (auth?.user?.role === 'supplier' || user?.role === 'supplier' ||
                              auth?.user?.role === 'retailer' || user?.role === 'retailer')
                    ? `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)} 0%, ${alpha(theme.palette.warning.main, 0.05)} 100%)`
                    : `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`,
                  border: (auth?.user?.role === 'supplier' || user?.role === 'supplier' ||
                          auth?.user?.role === 'retailer' || user?.role === 'retailer')
                    ? `1px solid ${alpha(theme.palette.warning.main, 0.3)}`
                    : `1px solid ${alpha(theme.palette.info.main, 0.3)}`,
                }}>
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '12px',
                      background: (auth?.user?.role === 'supplier' || user?.role === 'supplier' ||
                                  auth?.user?.role === 'retailer' || user?.role === 'retailer')
                        ? `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`
                        : `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.15)}`,
                    }}
                  >
                    {(auth?.user?.role === 'supplier' || user?.role === 'supplier' ||
                     auth?.user?.role === 'retailer' || user?.role === 'retailer') ? (
                      <ArrowBack sx={{ color: 'white', fontSize: 20, transform: 'rotate(180deg)' }} />
                    ) : (
                      <Security sx={{ color: 'white', fontSize: 20 }} />
                    )}
                  </Box>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                      {(auth?.user?.role === 'supplier' || user?.role === 'supplier' ||
                       auth?.user?.role === 'retailer' || user?.role === 'retailer')
                        ? 'Update Mode'
                        : 'Verification Mode'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      {(auth?.user?.role === 'supplier' || user?.role === 'supplier' ||
                       auth?.user?.role === 'retailer' || user?.role === 'retailer')
                        ? 'Modify product details after scanning'
                        : 'Check product authenticity and history'}
                    </Typography>
                  </Box>
                </StatusCard>
              </Box>
            )}

            {/* Enhanced Security Features */}
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} justifyContent="center" sx={{ mb: 6 }}>
              <StatusCard sx={{
                animation: `${smoothSlideIn} 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both`,
                background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.success.main, 0.05)} 100%)`,
                border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`,
              }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '12px',
                    background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: `0 4px 12px ${alpha(theme.palette.success.main, 0.3)}`,
                  }}
                >
                  <Security sx={{ color: 'white', fontSize: 20 }} />
                </Box>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                    Blockchain Secured
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                    Immutable verification records
                  </Typography>
                </Box>
              </StatusCard>

              <StatusCard sx={{
                animation: `${smoothSlideIn} 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both`,
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
              }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '12px',
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
                  }}
                >
                  <Fingerprint sx={{ color: 'white', fontSize: 20 }} />
                </Box>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 700, mb: 0.5 }}>
                    AI Verification
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                    Advanced pattern recognition
                  </Typography>
                </Box>
              </StatusCard>
            </Stack>
          </Box>

          {/* Enhanced Scanner Area */}
          <ScanArea sx={{ mb: 6 }}>
            {scanning ? (
              <>
                {useAlternativeScanner ? (
                  <AlternativeQrScanner passData={passData} />
                ) : (
                  <QrScanner passData={passData} />
                )}

                {/* Scanner Toggle */}
                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: 1,
                      opacity: 0.7,
                      fontSize: '0.875rem'
                    }}
                  >
                    Having scanning issues?
                  </Typography>
                  <Box
                    component="button"
                    onClick={() => setUseAlternativeScanner(!useAlternativeScanner)}
                    sx={{
                      background: 'none',
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                      borderRadius: '20px',
                      px: 3,
                      py: 1,
                      color: theme.palette.primary.main,
                      fontSize: '0.75rem',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.1),
                        borderColor: theme.palette.primary.main,
                      }
                    }}
                  >
                    {useAlternativeScanner ? 'Use Standard Scanner' : 'Try Alternative Scanner'}
                  </Box>
                </Box>
                <Typography
                  variant="h6"
                  sx={{
                    mt: 4,
                    fontWeight: 700,
                    letterSpacing: '0.1em',
                    display: 'flex',
                    alignItems: 'center',
                    textTransform: 'uppercase',
                    fontSize: '1rem',
                    color: theme.palette.primary.main,
                    fontFamily: '"JetBrains Mono", "Fira Code", monospace',
                    '&:before, &:after': {
                      content: '""',
                      flex: 1,
                      height: '3px',
                      background: `linear-gradient(90deg,
                        transparent,
                        ${theme.palette.primary.main},
                        ${theme.palette.info.main},
                        ${theme.palette.primary.dark},
                        transparent
                      )`,
                      mx: 3,
                      borderRadius: '2px',
                      animation: `${dataFlow} 3s ease-in-out infinite`,
                    },
                  }}
                >
                  Scanning Area Active
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mt: 2,
                    fontWeight: 500,
                    fontSize: '0.9rem',
                    opacity: 0.8,
                  }}
                >
                  Position QR code in center of frame for optimal detection
                </Typography>
              </>
            ) : (
              <Box sx={{ textAlign: 'center' }}>
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    background: `conic-gradient(from 0deg, ${theme.palette.primary.main}, ${theme.palette.info.main}, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 3,
                    animation: `${gentleFloat} 2s ease-in-out infinite`,
                    '&::before': {
                      content: '""',
                      width: '70%',
                      height: '70%',
                      borderRadius: '50%',
                      background: theme.palette.background.paper,
                      position: 'absolute',
                    },
                  }}
                >
                  <Security sx={{ fontSize: 32, color: theme.palette.primary.main, zIndex: 1 }} />
                </Box>
                <Typography variant="h5" sx={{ fontWeight: 700, mb: 2, color: theme.palette.primary.main }}>
                  Blockchain Verification
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                  Analyzing product credentials using advanced cryptographic verification...
                </Typography>
              </Box>
            )}
          </ScanArea>

          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 5,
                borderRadius: '16px',
                background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)} 0%, ${alpha(theme.palette.error.dark, 0.05)} 100%)`,
                border: `1px solid ${alpha(theme.palette.error.main, 0.3)}`,
                backdropFilter: 'blur(20px)',
                boxShadow: `0 8px 32px ${alpha(theme.palette.error.main, 0.15)}`,
                '& .MuiAlert-icon': {
                  fontSize: '1.5rem',
                },
                '& .MuiAlert-message': {
                  fontWeight: 600,
                },
              }}
            >
              {error}
            </Alert>
          )}

          {/* Enhanced Action Buttons */}
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} justifyContent="center" sx={{ mt: 6 }}>
            {scanning ? (
              <Box
                component="button"
                onClick={handleBack}
                sx={{
                  px: 6,
                  py: 3,
                  borderRadius: '16px',
                  border: `2px solid ${alpha(theme.palette.error.main, 0.3)}`,
                  background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)} 0%, ${alpha(theme.palette.error.main, 0.05)} 100%)`,
                  backdropFilter: 'blur(20px)',
                  color: theme.palette.error.main,
                  fontWeight: 700,
                  fontSize: '1.1rem',
                  fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                  cursor: 'pointer',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  boxShadow: `0 8px 32px ${alpha(theme.palette.error.main, 0.15)}`,
                  '&:hover': {
                    transform: 'translateY(-4px) scale(1.02)',
                    boxShadow: `0 12px 40px ${alpha(theme.palette.error.main, 0.25)}`,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.15)} 0%, ${alpha(theme.palette.error.main, 0.08)} 100%)`,
                    border: `2px solid ${alpha(theme.palette.error.main, 0.5)}`,
                  },
                  '&:active': {
                    transform: 'translateY(-2px) scale(1.01)',
                  },
                }}
              >
                <ArrowBack sx={{ fontSize: 20 }} />
                Cancel Scan
              </Box>
            ) : (
              <>
                <Box
                  component="button"
                  onClick={handleBack}
                  sx={{
                    px: 6,
                    py: 3,
                    borderRadius: '16px',
                    border: `2px solid ${alpha(theme.palette.divider, 0.3)}`,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
                    backdropFilter: 'blur(20px)',
                    color: theme.palette.text.primary,
                    fontWeight: 700,
                    fontSize: '1.1rem',
                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                    '&:hover': {
                      transform: 'translateY(-4px) scale(1.02)',
                      boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
                      border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                    },
                    '&:active': {
                      transform: 'translateY(-2px) scale(1.01)',
                    },
                  }}
                >
                  <ArrowBack sx={{ fontSize: 20 }} />
                  Back
                </Box>
                <Box
                  component="button"
                  onClick={handleRescan}
                  sx={{
                    px: 6,
                    py: 3,
                    borderRadius: '16px',
                    border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`,
                    background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                    backdropFilter: 'blur(20px)',
                    color: 'white',
                    fontWeight: 700,
                    fontSize: '1.1rem',
                    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
                    cursor: 'pointer',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    boxShadow: `0 8px 32px ${alpha(theme.palette.success.main, 0.3)}`,
                    '&:hover': {
                      transform: 'translateY(-4px) scale(1.05)',
                      boxShadow: `0 16px 50px ${alpha(theme.palette.success.main, 0.4)}`,
                      background: `linear-gradient(135deg, ${theme.palette.success.light} 0%, ${theme.palette.success.main} 100%)`,
                    },
                    '&:active': {
                      transform: 'translateY(-2px) scale(1.02)',
                    },
                  }}
                >
                  <Refresh sx={{ fontSize: 20 }} />
                  Scan Again
                </Box>
              </>
            )}
          </Stack>
        </Box>
      </Container>
    </Box>
  );
};

export default ScannerPage;
