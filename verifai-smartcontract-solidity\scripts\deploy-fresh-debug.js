const hre = require("hardhat");

async function main() {
    console.log("🚀 Deploying fresh VerifaiDebug contract...\n");

    try {
        // Get the contract factory
        const VerifaiDebugFactory = await hre.ethers.getContractFactory("VerifaiDebug");
        
        console.log("✅ Contract factory created");
        
        // Deploy the contract
        console.log("⏳ Deploying contract...");
        const contract = await VerifaiDebugFactory.deploy();
        await contract.deployed();

        const contractAddress = contract.address;
        console.log("✅ VerifaiDebug contract deployed successfully!");
        console.log("📍 Contract address:", contractAddress);
        
        // Test the contract immediately
        console.log("\n🔍 Testing the deployed contract...");
        
        // Test 1: Register a product
        console.log("📦 Step 1: Register test product...");
        const registerTx = await contract.registerProduct(
            "Test Product",
            "Test Brand", 
            "test123",
            "Test Description",
            "test.jpg",
            "Test Manufacturer",
            "Test Location",
            Math.floor(Date.now() / 1000).toString()
        );
        
        console.log("⏳ Registration transaction:", registerTx.hash);
        await registerTx.wait();
        console.log("✅ Product registered!");
        
        // Test 2: Check if product exists
        console.log("\n📦 Step 2: Check product exists...");
        const checkResult = await contract.checkProduct("test123");
        console.log("✅ Check result:");
        console.log("   Exists:", checkResult[0]);
        console.log("   Stored Serial:", checkResult[1]);
        console.log("   History Size:", checkResult[2].toString());
        
        // Test 3: Retrieve product
        console.log("\n📦 Step 3: Retrieve product...");
        const product = await contract.getProduct("test123");
        console.log("✅ Product retrieved successfully!");
        console.log("📋 Product details:");
        console.log("   Serial:", product[0]);
        console.log("   Name:", product[1]);
        console.log("   Brand:", product[2]);
        console.log("   Description:", product[3]);
        console.log("   Image:", product[4]);
        console.log("   History length:", product[5].length);
        
        if (product[5].length > 0) {
            console.log("   First history entry:");
            console.log("     Actor:", product[5][0].actor);
            console.log("     Location:", product[5][0].location);
            console.log("     Timestamp:", product[5][0].timestamp);
            console.log("     Is Sold:", product[5][0].isSold);
        }
        
        console.log("\n🎉 Contract deployment and testing successful!");
        console.log("📍 Working contract address:", contractAddress);
        console.log("🔗 QR Code format:", `${contractAddress},test123`);
        
        // Register the 'dffg' product that the user was trying to scan
        console.log("\n📦 Step 4: Registering 'dffg' product for user testing...");
        const dffgTx = await contract.registerProduct(
            "Sample Product",
            "Sample Brand",
            "dffg",
            "Test product for QR scanning",
            "sample.jpg",
            "Test Manufacturer",
            "Test Location",
            Math.floor(Date.now() / 1000).toString()
        );

        console.log("⏳ dffg registration transaction:", dffgTx.hash);
        await dffgTx.wait();
        console.log("✅ dffg product registered!");

        // Verify dffg product
        const checkDffg = await contract.checkProduct("dffg");
        console.log("✅ dffg check result:");
        console.log("   Exists:", checkDffg[0]);
        console.log("   Stored Serial:", checkDffg[1]);
        console.log("   History Size:", checkDffg[2].toString());

        // Update the centralized configuration
        console.log("\n🔄 Updating centralized configuration...");
        const fs = require('fs');
        const path = require('path');

        const configPath = path.join(__dirname, '../../project-config.json');
        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            config.blockchain.contractAddress = contractAddress;
            config.deployment.lastUpdated = new Date().toISOString();
            config.deployment.deployedBy = "deploy-fresh-debug.js";
            fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
            console.log("✅ Updated project-config.json");
        }
        
        console.log("\n📋 Next steps:");
        console.log("1. Run: node ../set-contract-address.js " + contractAddress);
        console.log("2. Test the frontend with the new contract address");
        console.log("3. Use QR code: " + contractAddress + ",test123");
        console.log("4. Use QR code: " + contractAddress + ",dffg");
        console.log("5. Both products are ready for scanning!");
        
    } catch (error) {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Script failed:", error);
        process.exit(1);
    });
