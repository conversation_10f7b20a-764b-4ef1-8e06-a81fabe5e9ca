const hre = require("hardhat");
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function main() {
    console.log("🚀 VERIFAI AUTO-RESTART SETUP");
    console.log("===============================");
    console.log("🔄 Automatically deploying contract and setting up everything...\n");

    try {
        // Step 1: Compile contracts
        console.log("📦 Step 1: Compiling smart contracts...");
        execSync('npx hardhat compile --force', { stdio: 'inherit' });
        console.log("✅ Contracts compiled successfully!\n");

        // Step 2: Deploy VerifaiDebug contract
        console.log("🚀 Step 2: Deploying VerifaiDebug contract...");
        const VerifaiDebugFactory = await hre.ethers.getContractFactory("VerifaiDebug");
        const contract = await VerifaiDebugFactory.deploy();
        await contract.deployed();
        
        const contractAddress = contract.address;
        console.log("✅ VerifaiDebug contract deployed successfully!");
        console.log("📍 Contract address:", contractAddress);

        // Step 3: Register test products
        console.log("\n📦 Step 3: Registering test products...");
        
        // Register test123 product
        console.log("   📋 Registering 'test123' product...");
        const test123Tx = await contract.registerProduct(
            "Test Product",
            "Test Brand",
            "test123",
            "Test Description",
            "test.jpg",
            "Test Manufacturer",
            "Test Location",
            Math.floor(Date.now() / 1000).toString()
        );
        await test123Tx.wait();
        console.log("   ✅ test123 product registered!");

        // Register dffg product (user's QR code)
        console.log("   📋 Registering 'dffg' product...");
        const dffgTx = await contract.registerProduct(
            "Sample Product",
            "Sample Brand",
            "dffg",
            "Test product for QR scanning",
            "sample.jpg",
            "Test Manufacturer",
            "Test Location",
            Math.floor(Date.now() / 1000).toString()
        );
        await dffgTx.wait();
        console.log("   ✅ dffg product registered!");

        // Step 4: Verify products are working
        console.log("\n🔍 Step 4: Verifying products...");
        
        const checkTest123 = await contract.checkProduct("test123");
        const checkDffg = await contract.checkProduct("dffg");
        
        console.log("   ✅ test123 exists:", checkTest123[0], "- History size:", checkTest123[2].toString());
        console.log("   ✅ dffg exists:", checkDffg[0], "- History size:", checkDffg[2].toString());

        // Step 5: Update project configuration
        console.log("\n🔄 Step 5: Updating project configuration...");
        
        const configPath = path.join(__dirname, '../../project-config.json');
        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            config.blockchain.contractAddress = contractAddress;
            config.deployment.lastUpdated = new Date().toISOString();
            config.deployment.deployedBy = "auto-restart-setup.js";
            config.deployment.testProductsRegistered = ["test123", "dffg"];
            fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
            console.log("   ✅ Updated project-config.json");
        }

        // Step 6: Update all frontend files
        console.log("\n🔄 Step 6: Updating all frontend files...");
        try {
            execSync(`cd .. && node set-contract-address.js ${contractAddress}`, { stdio: 'inherit' });
            console.log("   ✅ All frontend files updated!");
        } catch (error) {
            console.log("   ⚠️  Frontend update failed, but contract is deployed. Run manually:");
            console.log(`   node ../set-contract-address.js ${contractAddress}`);
        }

        // Step 7: Final verification
        console.log("\n🔍 Step 7: Final verification...");
        const finalTest123 = await contract.getProduct("test123");
        const finalDffg = await contract.getProduct("dffg");
        
        console.log("   ✅ test123 product details:", finalTest123[1]); // name
        console.log("   ✅ dffg product details:", finalDffg[1]); // name

        // Success summary
        console.log("\n🎉 AUTO-RESTART SETUP COMPLETE!");
        console.log("================================");
        console.log("✅ Contract deployed and verified");
        console.log("✅ Test products registered");
        console.log("✅ Frontend files updated");
        console.log("✅ Everything ready to use!");
        
        console.log("\n📋 Ready-to-use QR codes:");
        console.log(`   🔗 ${contractAddress},test123`);
        console.log(`   🔗 ${contractAddress},dffg`);
        
        console.log("\n🚀 Next steps:");
        console.log("1. Start your React frontend: npm run vite");
        console.log("2. Test QR scanning with the codes above");
        console.log("3. Register new products through the UI");
        console.log("4. Everything should work perfectly!");
        
        console.log("\n💡 TIP: Save this command for future restarts:");
        console.log("   npm run deploy-and-setup");

    } catch (error) {
        console.error("❌ Auto-restart setup failed:", error);
        console.log("\n🔧 Manual recovery steps:");
        console.log("1. Make sure Ganache is running on port 7545");
        console.log("2. Check MetaMask is connected to Ganache");
        console.log("3. Try running the script again");
        process.exit(1);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Script failed:", error);
        process.exit(1);
    });
