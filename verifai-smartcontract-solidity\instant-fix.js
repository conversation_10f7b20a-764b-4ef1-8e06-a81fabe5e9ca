const hre = require("hardhat");

async function main() {
    console.log("🚀 INSTANT FIX - Registering test product...");
    
    try {
        // Get the deployer account (this has funds)
        const [deployer] = await hre.ethers.getSigners();
        
        console.log("👤 Using account:", deployer.address);
        
        const balance = await deployer.getBalance();
        console.log("💰 Account balance:", hre.ethers.utils.formatEther(balance), "ETH");
        
        // Contract details
        const contractAddress = "******************************************";
        
        // Get contract factory and connect
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("📋 Contract Address:", contractAddress);
        console.log("✅ Connected to contract successfully");
        
        // Product details
        const productData = {
            name: "Quick Fix Product",
            brand: "Demo Brand", 
            serialNumber: "QUICKFIX123",
            description: "Instant test product for scanning",
            image: "fix.jpg",
            actor: "Quick Manufacturer",
            location: "Fix Location",
            timestamp: Math.floor(Date.now() / 1000).toString()
        };
        
        console.log("📦 Registering product:", productData.serialNumber);
        
        // Register the product
        const registerTx = await contract.registerProduct(
            productData.name,
            productData.brand,
            productData.serialNumber,
            productData.description,
            productData.image,
            productData.actor,
            productData.location,
            productData.timestamp
        );
        
        console.log("⏳ Transaction hash:", registerTx.hash);
        await registerTx.wait();
        console.log("✅ Product registered successfully!");
        
        // Verify the product
        const product = await contract.getProduct(productData.serialNumber);
        console.log("✅ Product verified!");
        console.log("📦 Product details:");
        console.log("   Serial Number:", product[0]);
        console.log("   Name:", product[1]);
        console.log("   Brand:", product[2]);
        
        console.log("\n🎯 QR Code for testing:");
        console.log(`${contractAddress},${productData.serialNumber}`);
        
        console.log("\n✅ INSTANT FIX COMPLETE!");
        console.log("🔗 Your scanning error is now FIXED!");
        console.log("🎯 Test with this QR code in your frontend");
        
    } catch (error) {
        console.error("❌ Error:", error.message);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Script failed:", error);
        process.exit(1);
    });
