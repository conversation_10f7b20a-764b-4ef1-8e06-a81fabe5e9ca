#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

console.log("🚀 VERIFAI PROJECT RESTART");
console.log("===========================");
console.log("🔄 Fixing contract deployment issues automatically...\n");

try {
    // Change to smart contract directory
    const smartContractDir = path.join(__dirname, 'verifai-smartcontract-solidity');
    
    console.log("📂 Changing to smart contract directory...");
    process.chdir(smartContractDir);
    
    console.log("🚀 Running automated deployment and setup...");
    console.log("This will:");
    console.log("  ✅ Compile smart contracts");
    console.log("  ✅ Deploy VerifaiDebug contract");
    console.log("  ✅ Register test products (test123, dffg)");
    console.log("  ✅ Update all frontend files");
    console.log("  ✅ Verify everything is working\n");
    
    // Run the auto-restart setup
    execSync('npm run deploy-and-setup', { stdio: 'inherit' });
    
    console.log("\n🎉 PROJECT RESTART COMPLETE!");
    console.log("=============================");
    console.log("✅ Contract deployed and configured");
    console.log("✅ Test products registered");
    console.log("✅ Frontend files updated");
    console.log("✅ Ready to use!");
    
    console.log("\n🚀 Next steps:");
    console.log("1. cd verifai-frontend-react");
    console.log("2. npm run vite");
    console.log("3. Test QR scanning and product registration");
    
    console.log("\n💡 For future restarts, just run:");
    console.log("   node restart-verifai.js");
    
} catch (error) {
    console.error("\n❌ Restart failed:", error.message);
    console.log("\n🔧 Manual steps to try:");
    console.log("1. Make sure Ganache is running on port 7545");
    console.log("2. cd verifai-smartcontract-solidity");
    console.log("3. npm run deploy-and-setup");
    console.log("4. If that fails, check Ganache connection");
    
    process.exit(1);
}
