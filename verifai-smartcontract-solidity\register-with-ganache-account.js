const hre = require("hardhat");

async function main() {
    console.log("🚀 Registering test product with Ganache account...");
    
    try {
        // Use a specific Ganache account with known private key
        const provider = new hre.ethers.providers.JsonRpcProvider("http://127.0.0.1:7545");
        
        // Use the first Ganache account (this should have funds)
        const privateKey = "0x4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1d";
        const wallet = new hre.ethers.Wallet(privateKey, provider);
        
        console.log("👤 Using account:", wallet.address);
        
        const balance = await wallet.getBalance();
        console.log("💰 Account balance:", hre.ethers.utils.formatEther(balance), "ETH");
        
        if (balance.eq(0)) {
            console.log("❌ Account has no funds. Make sure Ganache is running with the default accounts.");
            return;
        }
        
        // Contract details
        const contractAddress = "******************************************";
        
        // Get contract factory and connect with wallet
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        const contract = VerifaiFactory.attach(contractAddress).connect(wallet);
        
        console.log("📋 Contract Address:", contractAddress);
        console.log("✅ Connected to contract successfully");
        
        // Product details
        const productData = {
            name: "Demo Product",
            brand: "Demo Brand", 
            serialNumber: "DEMO123",
            description: "Test product for scanning demo",
            image: "demo.jpg",
            actor: "Demo Manufacturer",
            location: "Demo Location",
            timestamp: Math.floor(Date.now() / 1000).toString()
        };
        
        console.log("📦 Registering product:", productData.serialNumber);
        
        // Register the product
        const registerTx = await contract.registerProduct(
            productData.name,
            productData.brand,
            productData.serialNumber,
            productData.description,
            productData.image,
            productData.actor,
            productData.location,
            productData.timestamp,
            {
                gasLimit: 3000000,
                gasPrice: hre.ethers.utils.parseUnits("20", "gwei")
            }
        );
        
        console.log("⏳ Transaction hash:", registerTx.hash);
        await registerTx.wait();
        console.log("✅ Product registered successfully!");
        
        // Verify the product
        const product = await contract.getProduct(productData.serialNumber);
        console.log("✅ Product verified!");
        console.log("📦 Product details:");
        console.log("   Serial Number:", product[0]);
        console.log("   Name:", product[1]);
        console.log("   Brand:", product[2]);
        console.log("   Description:", product[3]);
        console.log("   Image:", product[4]);
        
        console.log("\n🎯 QR Code for testing:");
        console.log(`${contractAddress},${productData.serialNumber}`);
        
        console.log("\n✅ SUCCESS! Your demo product is ready!");
        console.log("🔗 You can now scan this QR code in your frontend");
        
    } catch (error) {
        console.error("❌ Error:", error.message);
        
        if (error.message.includes("insufficient funds")) {
            console.log("\n💡 Make sure Ganache is running with default accounts");
        } else if (error.message.includes("ECONNREFUSED")) {
            console.log("\n💡 Make sure Ganache is running on http://127.0.0.1:7545");
        }
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Script failed:", error);
        process.exit(1);
    });
