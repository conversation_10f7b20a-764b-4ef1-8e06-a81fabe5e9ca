const hre = require("hardhat");

async function main() {
    console.log("🔍 Testing 'dffg' product registration...\n");

    try {
        // Get the contract factory
        const VerifaiDebugFactory = await hre.ethers.getContractFactory("VerifaiDebug");
        
        // Contract address
        const contractAddress = "******************************************";
        
        console.log("📋 Contract Address:", contractAddress);
        
        // Connect to the deployed contract
        const contract = VerifaiDebugFactory.attach(contractAddress);
        
        console.log("✅ Connected to contract successfully");
        
        // First, let's check if 'test123' exists (we know this should work)
        console.log("\n🔍 Step 1: Testing with known working product 'test123'...");
        try {
            const checkTest123 = await contract.checkProduct("test123");
            console.log("✅ test123 check result:");
            console.log("   Exists:", checkTest123[0]);
            console.log("   Stored Serial:", checkTest123[1]);
            console.log("   History Size:", checkTest123[2].toString());
        } catch (error) {
            console.log("❌ test123 check failed:", error.message);
        }
        
        // Now let's register 'dffg' product
        console.log("\n🔍 Step 2: Registering 'dffg' product...");
        
        const productData = {
            name: "Sample Product",
            brand: "Sample Brand", 
            serialNumber: "dffg",
            description: "Test product for blockchain verification",
            image: "test-image.jpg",
            actor: "Test Manufacturer",
            location: "Test Location",
            timestamp: Math.floor(Date.now() / 1000).toString()
        };
        
        console.log("📦 Registering product:", productData.serialNumber);
        
        const registerTx = await contract.registerProduct(
            productData.name,
            productData.brand,
            productData.serialNumber,
            productData.description,
            productData.image,
            productData.actor,
            productData.location,
            productData.timestamp
        );
        
        console.log("⏳ Transaction submitted:", registerTx.hash);
        const receipt = await registerTx.wait();
        console.log("✅ Transaction confirmed in block:", receipt.blockNumber);
        
        // Now test if we can check the product
        console.log("\n🔍 Step 3: Testing 'dffg' product check...");
        try {
            const checkDffg = await contract.checkProduct("dffg");
            console.log("✅ dffg check result:");
            console.log("   Exists:", checkDffg[0]);
            console.log("   Stored Serial:", checkDffg[1]);
            console.log("   History Size:", checkDffg[2].toString());
            
            if (checkDffg[0]) {
                // Now try to get the full product
                console.log("\n🔍 Step 4: Getting full 'dffg' product details...");
                const product = await contract.getProduct("dffg");
                
                console.log("✅ dffg product retrieved successfully!");
                console.log("📋 Product details:");
                console.log("   Serial:", product[0]);
                console.log("   Name:", product[1]);
                console.log("   Brand:", product[2]);
                console.log("   Description:", product[3]);
                console.log("   Image:", product[4]);
                console.log("   History length:", product[5].length);
                
                if (product[5].length > 0) {
                    console.log("📋 History:");
                    product[5].forEach((entry, index) => {
                        console.log(`   ${index + 1}. Actor: ${entry.actor}, Location: ${entry.location}, Sold: ${entry.isSold}`);
                    });
                }
                
                console.log("\n🎉 SUCCESS! Product 'dffg' is now registered and retrievable!");
                console.log("🔗 QR Code format: ******************************************,dffg");
                
            } else {
                console.log("❌ Product 'dffg' was not found after registration");
            }
            
        } catch (error) {
            console.log("❌ dffg check failed:", error.message);
        }
        
    } catch (error) {
        console.error("❌ Script failed:", error);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Script failed:", error);
        process.exit(1);
    });
