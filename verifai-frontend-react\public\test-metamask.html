<!DOCTYPE html>
<html>
<head>
    <title>MetaMask Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 MetaMask Connection Test</h1>
    <div id="status"></div>
    
    <button onclick="testConnection()">Test Connection</button>
    <button onclick="testContract()">Test Contract</button>
    
    <script>
        const CONTRACT_ADDRESS = "0xA5FADc335E030bee0f3A9Cb2e01F18a2855F332d";
        
        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            statusDiv.appendChild(div);
            console.log(message);
        }
        
        async function testConnection() {
            try {
                log("🔍 Testing MetaMask connection...");
                
                if (typeof window.ethereum === 'undefined') {
                    log("❌ MetaMask not detected", 'error');
                    return;
                }
                
                log("✅ MetaMask detected", 'success');
                
                const accounts = await window.ethereum.request({ 
                    method: 'eth_requestAccounts' 
                });
                
                if (accounts.length > 0) {
                    log(`✅ Connected to account: ${accounts[0]}`, 'success');
                } else {
                    log("❌ No accounts found", 'error');
                }
                
                const chainId = await window.ethereum.request({ 
                    method: 'eth_chainId' 
                });
                
                log(`🔗 Chain ID: ${chainId} (${parseInt(chainId, 16)})`, 'info');
                
                if (parseInt(chainId, 16) === 1337) {
                    log("✅ Connected to Hardhat network", 'success');
                } else {
                    log("⚠️ Not connected to Hardhat network (1337)", 'error');
                }
                
            } catch (error) {
                log(`❌ Connection error: ${error.message}`, 'error');
            }
        }
        
        async function testContract() {
            try {
                log("🔍 Testing contract interaction...");
                
                if (typeof window.ethereum === 'undefined') {
                    log("❌ MetaMask not detected", 'error');
                    return;
                }
                
                // Simple RPC call to check if contract exists
                const code = await window.ethereum.request({
                    method: 'eth_getCode',
                    params: [CONTRACT_ADDRESS, 'latest']
                });
                
                if (code === '0x') {
                    log(`❌ No contract found at ${CONTRACT_ADDRESS}`, 'error');
                } else {
                    log(`✅ Contract found at ${CONTRACT_ADDRESS}`, 'success');
                    log(`📝 Contract code size: ${code.length} bytes`, 'info');
                }
                
            } catch (error) {
                log(`❌ Contract test error: ${error.message}`, 'error');
            }
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            log("🚀 Page loaded, ready for testing");
        });
    </script>
</body>
</html>
