const hre = require("hardhat");

async function main() {
    console.log("🚀 Quick product registration for demo...");
    
    try {
        // Get the contract factory
        const VerifaiFactory = await hre.ethers.getContractFactory("Verifai");
        
        // Contract address
        const contractAddress = "******************************************";
        
        // Connect to the deployed contract
        const contract = VerifaiFactory.attach(contractAddress);
        
        console.log("📋 Contract Address:", contractAddress);
        console.log("✅ Connected to contract successfully");
        
        // Product details
        const productData = {
            name: "Demo Product",
            brand: "Demo Brand", 
            serialNumber: "DEMO123",
            description: "Test product for scanning demo",
            image: "demo.jpg",
            actor: "Demo Manufacturer",
            location: "Demo Location",
            timestamp: Math.floor(Date.now() / 1000).toString()
        };
        
        console.log("📦 Registering product:", productData.serialNumber);
        
        // Register the product
        const registerTx = await contract.registerProduct(
            productData.name,
            productData.brand,
            productData.serialNumber,
            productData.description,
            productData.image,
            productData.actor,
            productData.location,
            productData.timestamp
        );
        
        console.log("⏳ Transaction hash:", registerTx.hash);
        await registerTx.wait();
        console.log("✅ Product registered successfully!");
        
        // Verify the product
        const product = await contract.getProduct(productData.serialNumber);
        console.log("✅ Product verified!");
        console.log("📦 Product name:", product[1]);
        console.log("🏷️  Product brand:", product[2]);
        
        console.log("\n🎯 QR Code for testing:");
        console.log(`${contractAddress},${productData.serialNumber}`);
        
        console.log("\n✅ SUCCESS! Your demo product is ready!");
        
    } catch (error) {
        console.error("❌ Error:", error.message);
        
        if (error.message.includes("insufficient funds")) {
            console.log("\n💡 Solution: Make sure you have a funded Ganache account imported in MetaMask");
            console.log("   1. Open Ganache application");
            console.log("   2. Copy a private key from one of the accounts");
            console.log("   3. Import it into MetaMask");
            console.log("   4. Make sure MetaMask is connected to Ganache (http://127.0.0.1:7545)");
        }
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Script failed:", error);
        process.exit(1);
    });
