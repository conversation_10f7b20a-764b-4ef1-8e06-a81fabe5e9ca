# 🔰 **BEGINNER'S RESTART GUIDE - Verifai Project**

## 🎯 **When to Use This Guide**

**Use this guide when:**
- ✅ You restart your computer/terminal
- ✅ You close and reopen your project
- ✅ You get contract deployment errors
- ✅ QR scanning shows "fake product" instead of real product
- ✅ You see "could not decode result data" errors

## 📂 **Step 1: Find Your Project Folder**

**Your project folder structure should look like this:**

```
📁 Verifai/                          ← YOU NEED TO BE HERE!
├── 📁 verifai-frontend-react/       ← React frontend
├── 📁 verifai-smartcontract-solidity/ ← Smart contracts  
├── 📁 Verifai-backend/              ← Backend server
├── 📄 restart-verifai.js            ← Restart script (should be here)
├── 📄 package.json                  ← NPM scripts (should be here)
└── 📄 project-config.json           ← Configuration
```

### **🔍 How to Navigate to the Right Folder:**

**Option A: Using File Explorer (Windows)**
1. Open File Explorer
2. Navigate to your project (usually in Documents or Desktop)
3. Find the **Verifai** folder (the main one)
4. Right-click inside the folder → "Open in Terminal" or "Open PowerShell window here"

**Option B: Using Terminal Commands**
```bash
# Example paths (adjust to your actual location):
cd "C:\Users\<USER>\Documents\GitHub\anti-counterfeitproduct\Verifai"

# Or if it's on Desktop:
cd "C:\Users\<USER>\Desktop\Verifai"

# Or if it's in Downloads:
cd "C:\Users\<USER>\Downloads\Verifai"
```

### **✅ Verify You're in the Right Place:**
```bash
# Run this command to see what's in your current folder:
dir

# You should see these folders/files:
# verifai-frontend-react
# verifai-smartcontract-solidity
# Verifai-backend
# restart-verifai.js
# package.json
```

**❌ If you DON'T see these folders, you're in the wrong place!**

## 🚀 **Step 2: Start Ganache**

**Before running any commands, Ganache MUST be running:**

1. **Open Ganache Desktop Application**
2. **Click "Quickstart"** (or open existing workspace)
3. **Verify settings:**
   - Port: 7545
   - Chain ID: 1337
   - 10 accounts with 100 ETH each

**✅ Ganache is ready when you see the accounts list with ETH balances**

## 🎯 **Step 3: Run the Magic Command**

**Now that you're in the right folder and Ganache is running:**

```bash
# THE MAGIC COMMAND THAT FIXES EVERYTHING:
npm run restart-project
```

**What you'll see:**
```
🚀 VERIFAI PROJECT RESTART
===========================
🔄 Fixing contract deployment issues automatically...

📦 Step 1: Compiling smart contracts...
✅ Contracts compiled successfully!

🚀 Step 2: Deploying VerifaiDebug contract...
✅ VerifaiDebug contract deployed successfully!

📦 Step 3: Registering test products...
✅ test123 product registered!
✅ dffg product registered!

🔄 Step 6: Updating all frontend files...
✅ All frontend files updated!

🎉 PROJECT RESTART COMPLETE!
```

## 🎉 **Step 4: Start Your Frontend**

**After the restart command succeeds:**

```bash
# Navigate to frontend folder:
cd verifai-frontend-react

# Start the development server:
npm run vite

# You should see:
# Local: http://localhost:5173/
```

## ✅ **Step 5: Test Everything Works**

1. **Open browser**: Go to http://localhost:5173/
2. **Login**: Use testmanu/testpass123
3. **Register a product**: Fill the form and submit
4. **Expected**: QR code appears in 3-8 seconds
5. **Test scanning**: Use the QR codes provided by the restart command

## 🚨 **Common Beginner Mistakes & Solutions**

### **❌ "npm run restart-project" command not found**
**Problem**: You're in the wrong folder
**Solution**: 
1. Run `dir` command
2. If you don't see `package.json`, navigate to the main Verifai folder
3. Try the command again

### **❌ "Ganache connection failed"**
**Problem**: Ganache is not running
**Solution**: 
1. Open Ganache Desktop app
2. Click "Quickstart"
3. Wait for it to load completely
4. Try the restart command again

### **❌ "Cannot find module" errors**
**Problem**: Dependencies not installed
**Solution**: 
```bash
# Install dependencies first:
npm install

# Then try restart command:
npm run restart-project
```

### **❌ Still getting contract errors**
**Problem**: Old browser cache or MetaMask issues
**Solution**: 
1. Clear browser cache (Ctrl+Shift+Delete)
2. Reset MetaMask account (Settings → Advanced → Reset Account)
3. Re-import Ganache account to MetaMask

## 💡 **Pro Tips for Beginners**

1. **Bookmark the restart command**: `npm run restart-project`
2. **Always start Ganache first** before running any commands
3. **Keep Ganache running** while developing
4. **Use the same terminal** for consistency
5. **If something breaks, run the restart command again**

## 🎯 **Quick Reference Card**

**Every time you restart your computer/terminal:**

1. ✅ **Start Ganache** (Desktop app → Quickstart)
2. ✅ **Open terminal in main Verifai folder**
3. ✅ **Run**: `npm run restart-project`
4. ✅ **Start frontend**: `cd verifai-frontend-react && npm run vite`
5. ✅ **Test in browser**: http://localhost:5173/

**That's it! Your project will work perfectly every time! 🎉**
